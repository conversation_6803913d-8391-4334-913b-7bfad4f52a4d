/*
 * @Date: 2022-01-06 14:24:43
 * @Description: 找活相关
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import type { ITemplateList } from './index.d'
import { resumeCompleteDef, globalConfigDef, resumeThreePerfectStepDef, resumeExistDef } from './data'
import { getFilterData } from '@/utils/helper/resume/index'
import { validator } from '@/utils/tools/index'
import { actions as gActions, dispatch as gDispatch } from '@/store/index'
import { deepClone } from '@/lib/mini/utils/utils'

type BuryingPointData = {
  /** 找活名片ID */
  info_id?: string
  /** 找活子名片ID */
  resume_uuid?: string
  /** 请求id 程序随机生成的唯一标识 */
  request_id?: string
  /** 位置id 1、2、3等等（总的排序值） */
  location_id?: string
  /** 分页id 1、2、3等等 */
  pagination?: string
  /** 信息在当前页面位置 1、2、3等等 */
  pagination_location?: string
  /** 进入来源ID（即详情页上一个页面） (1-找活列表页、2-搜索结果页、4-附近适合您的工人、5-谁看过我的招工底部推荐、16-物流列表、17--工厂列表) */
  source_id?: string
  /** 活跃状态 0、1（0-未活跃、1-活跃中） */
  active_status?: string
  /** 距离(km) */
  post_distance?: string
  /** 搜索结果 */
  search_result?: string
  /** 点击入口 1、2、3(1-页面底部、2-页面中间、3-聊一聊) */
  click_entry?: string
  /** 拨打间隔时长(秒) */
  dialing_interval_duration?: string
  /** 获取状态 0、1、2（0-获取失败、1-获取成功（首次）、2-获取成功（非首次）） */
  get_status?: string
  /** 算法ID */
  backend_id?: string
}

const { reducer, actions, name } = createSlice({
  name: 'resume',
  initialState: {
    /** 别人的找活卡片详情数据 */
    resumeDetailsOther: {} as any,
    /** 别人的找活详情requestData */
    requestData: {},
    /** 因接口不一致，仅带入部分优先级高的数据 */
    info: {
      id: '',
      // 找活数据的uuid
      uuid: '',
    },
    /** 名片全局配置接口 v4.0.0 */
    globalConfig: { ...globalConfigDef } as YModels['POST/resume/v3/common/globalConfig']['Res']['data'],
    /** 根据用户ID查询模板-找活通用配置模板 v4.0.0 */
    resumeTemplates: [] as ITemplateList,
    /** 完善度阶段信息 v4.0.0 */
    resumeComplete: { ...resumeCompleteDef } as YModels['POST/resume/v1/resumePerfect/getProgressStageInfo']['Res']['data'],
    /** 是否含有找活名片 v4.0.0 resumeExist. */
    resumeExist: { ...resumeExistDef },
    /** 在新牛人完善流程中，选择的是蓝领还是白领 'blue' | 'white' */
    chooseJobRole: '',
    /** 简历完善流程 */
    resumeProcess: {} as YModels['POST/resume/v3/prepub/getConfig']['Res']['data'],
    /** 简历完善流程值 */
    processValue: {} as YModels['POST/resume/v3/prepub/getModuleValues']['Res']['data'],
    /** 专业类别数据 */
    majorData: [] as Array<{
      label: string
      value: string | number
      children: Array<{
        label: string
        value: string
      }>
    }>,
    /** 新牛人流程（职场人/学生）- 身份对应的所有已填写的数据
     * （**切换身份只保留公共的信息，后面的步骤会重置)
     * 
     * 数据example:
     * newWorkFillInProcessJsonData = {
            "eduExp": :{
              "eduType": 1,
              "eduBackground": 4,
              "schoolName": "天府新区信息职业学院",
              "majorName": "化学测量学与技术",
              "majorType": 8,
              "majorSubType": 72,
              "startTime": "2020",
              "endTime": "2024"
            },
            "workExp": {
                "companyId": null,
                "companyName": null,
                "startTime": null,
                "endTime": null,
                "occId": null,
                "detail": null,
                "firstWorkTime": null
            },
            "jobExpect": {
              "occupations": [
                  {
                      "occupation": 599,
                      "industries": [

                      ],
                      "mode": null,
                      "positionType": null
                  },
                  {
                      "occupation": 1111,
                      "industries": [

                      ],
                      "mode": null,
                      "positionType": null
                  },
                  {
                      "occupation": 768,
                      "industries": [

                      ],
                      "mode": null,
                      "positionType": null
                  }
                ],
                "hopeArea": [
                    322,
                    3328,
                    3329
                ]
              },
            "baseInfo": {
                "gender": 2,
                "name": "传达本算法",
                "birthDay": "1993-06",
            },
            "introduce": null,
            "hopeSalary": "2,10000,15000,2",
            "headPortrait": ""
        }
     * */
    newWorkFillInProcessJsonData: {},
  },
  reducers: {
    setState(state, { payload }: PayloadAction<Record<string, any>>) {
      Object.assign(state, payload)
    },
    setInfo(state, { payload }) {
      state.info = payload
    },
    setRequestData(state, { payload }) {
      state.requestData = payload
    },
    setProcessValue(state, { payload }) {
      const [key] = Object.keys(payload)
      Object.assign(state.processValue, { [key]: typeof payload[key] !== 'object' ? payload[key] : { ...(state.processValue[key] || {}), ...(payload[key]) } })
    },
    /** 设置专业类别数据 */
    setMajorData(state, { payload }) {
      state.majorData = payload
    },
    /** 更新新牛人流程填写的数据 */
    setNewWorkFillInProcessJsonData(state, { payload }) {
      const { moduleType, data } = payload
      if (moduleType && data) {
        state.newWorkFillInProcessJsonData = {
          ...state.newWorkFillInProcessJsonData,
          [moduleType]: {
            ...(state.newWorkFillInProcessJsonData[moduleType] || {}),
            ...data,
          },
        }
      }
    },
  },
})

/**
 * 存储详情需要的地址
 * @param {Object} payload 参数
 * @param {String} payload.uuid 找活uuid
 * @param {String} payload.originType '' | 'logistics' | 'factory' ,来源类型
 * @param {Number[]} payload.cities 列表筛选的城市
 * @param {Number[]} payload.occupations 列表筛选的工种
 * @param {Boolean} payload.isSlide 是否是滑动
 * @returns
 */
const fetchGetDetailOther = (payload) => (dispatch, getState) => {
  return new Promise(async (resolve) => {
    const filterData = await getFilterData()
    const cities = wx.$.u.isArrayVal(payload.cities) ? payload.cities : filterData.area_id
    const occupations = wx.$.u.isArrayVal(payload.occupations) ? payload.occupations : filterData.classify_id
    let scene = 0
    if (payload.isSearchList) {
      scene = payload.isSlide ? 3 : 2
    } else {
      scene = payload.isSlide ? 1 : 0
    }

    const params = {
      resumeSubUuid: payload.uuid,
      purchaseBaseReq: {
        cities,
        occupations,
      },
      scene,
      jobId: payload.jobId,
    }
    wx.$.javafetch['POST/resume/v3/detail/app/otherDetail'](params)
      .then((res) => {
        dispatch(actions.setRequestData({ ...(payload.isSlide ? getState().resume.requestData : {}), [params.resumeSubUuid]: res }))
        resolve(res)
      })
      .catch((res) => {
        dispatch(actions.setRequestData({ ...(payload.isSlide ? getState().resume.requestData : {}), [params.resumeSubUuid]: res }))
        resolve(res)
      })
  })
}

/** 名片全局配置接口 v4.0.0 */
const fetchGlobalConfig = (isUpdate = false) => (dispatch, getState): Promise<typeof globalConfigDef> => {
  return new Promise((resolve, reject) => {
    const globalConfig = getState().resume.globalConfig || {}
    if (!isUpdate && Object.keys(globalConfig).length > 3 && globalConfig.rightsConfigResp) {
      resolve(globalConfig)
      return
    }

    wx.$.javafetch['POST/resume/v3/common/globalConfig']({}, { hideMsg: true })
      .then((res) => {
        if (res.error) {
          dispatch(actions.setState({ globalConfig: { ...globalConfigDef } }))
          reject(res)
        } else {
          dispatch(actions.setState({ globalConfig: res.data || { ...globalConfigDef } }))
          resolve(res.data || { ...globalConfigDef })
        }
      })
      .catch((res) => {
        dispatch(actions.setState({ globalConfig: { ...globalConfigDef } }))
        reject(res)
      })
  })
}

/** 更新是否含有找活名片的数据7.0.0 */
const fetchResumeExist = (isUpdate = false) => (dispatch, getState) => {
  return new Promise((resolve) => {
    const resumeExist = getState().resume.resumeExist || { ...resumeExistDef }
    const { login } = getState().storage.userState
    if (!login || (!isUpdate && !resumeExist.isDef && resumeExist.resumeUuid)) {
      resolve({ ...resumeExist, res: null })
      return
    }
    wx.$.javafetch['POST/resume/v3/base/exist']({}, { isNoToken: true, hideMsg: true }).then((res) => {
      if (res.error) {
        dispatch(actions.setState({ resumeExist: { ...resumeExistDef } }))
        resolve({ ...resumeExistDef, res })
        return
      }
      if (res && res.data && !res.data.exist) {
        // 如果简历不存在，清空简历信息
        gDispatch(gActions.storageActions.removeItem('myResumeDetails'))
      }
      dispatch(actions.setState({ resumeExist: res.data || { ...resumeExistDef } }))
      const data = res.data || { ...resumeExistDef }
      resolve({ ...data, res })
    }).catch((err) => {
      dispatch(actions.setState({ resumeExist: { ...resumeExistDef } }))
      resolve({ ...resumeExistDef, res: err })
    })
  }) as Promise<YModels['POST/resume/v3/base/exist']['Res']['data'] & {res: any}>
}

/** 获取简历完善流程配置 */
const getResumeConfig = (isUpdate = false, jobOccIds?) => (dispatch, getState) => {
  return new Promise(async (resolve, reject) => {
    let resumeProcess = getState().resume.resumeProcess || {}
    let moduleValues = getState().resume.processValue || {}
    let params = { scene: 1 } as any
    if (!isUpdate && Object.keys(resumeProcess).length) {
      resolve({ ...resumeProcess, moduleValues })
      return
    }
    // 连接受限添加简历完善流程
    if (jobOccIds) {
      params = { scene: 2, jobOccIds }
    }
    // 兼容职场人/学生两种角色的 流程配置
    const newConfigRes = await wx.$.javafetch['POST/resume/v3/newflow/init/data'](params, { hideMsg: true }).catch((res) => res)
    const newConfig = newConfigRes.data || {}
    moduleValues = newConfigRes?.userData?.userFormData

    // 如果流程已完成（获取流程配置失败） || 流程已完成 || 获取不到下一步
    if (!newConfigRes.data || newConfig?.nextStep?.isCompleted || !newConfig.nextStep?.nextStep) {
      resolve({ processList: [] })
      return
    }

    // 公共函数：转换步骤数据格式(为了兼容之前的字段逻辑)
    const transformStepsItem = (steps: any[]) => {
      return (wx.$.u.isArrayVal(steps) ? steps : []).map(ele => ({
        ...ele,
        type: ele.moduleCode,
        name: ele.stepCode,
        jumpSwitch: ele.canSkip,
      }))
    }

    const commonConfigList = transformStepsItem(newConfig.commonConfig?.steps)

    // 过滤白领数据：移除与公共数据中 stepCode 和 moduleCode 都相同的项
    const whiteConfigList = transformStepsItem(newConfig?.configs?.white_collar?.steps)
    const whiteFilterConfigList = whiteConfigList.filter(whiteItem => {
      return !commonConfigList.some(commonItem => commonItem.name === whiteItem.name && commonItem.type === whiteItem.type)
    })

    // 过滤蓝领数据：移除与公共数据中 stepCode 和 moduleCode 都相同的项
    const blueConfigList = transformStepsItem(newConfig?.configs?.blue_collar?.steps)
    const blueFilterConfigList = blueConfigList.filter(blueItem => {
      return !commonConfigList.some(commonItem => commonItem.name === blueItem.name && commonItem.type === blueItem.type)
    })

    // 所有的配置流程数据--先获取公共步骤
    let processList = deepClone(commonConfigList)

    // 判断身份（职场人/学生）
    if (newConfig.userData.identityType) {
      // 如果已经选了蓝领/白领 : 工种类型 null,white_collar为白领流程,blue_collar为蓝领流程
      if (newConfig.classification) {
        const collar = newConfig.classification == 'white_collar' ? 'white_collar' : 'blue_collar'
        processList = transformStepsItem(newConfig?.config?.[collar]?.steps)
      } else if (newConfig.userData.identityType == 'student') {
        processList = transformStepsItem(newConfig?.configs?.student?.steps)
      } else {
        // 用户在身份选择的时候，点击跳过。默认为：职场人身份，求职状态为 离职-随时到岗处理。 此时跳转到 期望职位去选工种 (取白领）。
        processList = transformStepsItem(newConfig?.configs?.white_collar?.steps)
      }
    }

    // !! 需要删除
    // const configRes = await wx.$.javafetch['POST/resume/v3/prepub/getSceneConfig'](params, { hideMsg: true }).catch((res) => res)
    const valuesRes = await wx.$.javafetch['POST/resume/v3/prepub/getModuleValues']({}, { hideMsg: true }).catch((res) => res)
    // const config = configRes.data || {}
    // moduleValues = valuesRes.data || {}

    // if (!configRes.data || config.isFinished || !config.item) {
    //   resolve({ processList: [] })
    //   return
    // }
    // let processList = (wx.$.u.isArrayVal(config.commons) ? config.commons : []).flatMap(item => (item.items || []).map(ele => ({ ...ele, type: item.type })))

    // if (config.classification) {
    //   const list = config.classification == 'white' ? config.whites : config.blues
    //   const process = (wx.$.u.isArrayVal(list) ? list : []).flatMap(item => (item.items || []).map(ele => ({ ...ele, type: item.type })))
    //   processList = processList.concat(process)
    // }

    const config = {
      item: newConfig?.nextStep?.nextStep, // 下一个步骤的模块标识
      module: processList?.length > 0 ? processList.find((item) => item?.name == newConfig?.nextStep?.nextStep)?.moduleCode || '' : '', // 下一个步骤的大模块标识
      classification: newConfig?.classification,
      isFinished: newConfig?.nextStep?.isCompleted,
      commons: commonConfigList,
      whites: whiteFilterConfigList,
      blues: blueFilterConfigList,
    }
    console.log('processConfigconfigconfigconfig', config)
    /** 简历完善流程 */
    resumeProcess = {
      ...config,
      processList,
      jobOccIds,
    }
    dispatch(actions.setState({ resumeProcess, processValue: moduleValues }))
    resolve({ ...resumeProcess, moduleValues })
  }) as Promise<YModels['POST/resume/v3/prepub/getConfig']['Res']['data'] & { moduleValues?: YModels['POST/resume/v3/prepub/getModuleValues']['Res']['data']} & {processList?: { type?: string; name?: string; jumpSwitch?: boolean; }[]}>
}

/** 获取专业类别数据 */
const fetchMajorData = (isUpdate = false) => (dispatch, getState) => {
  return new Promise((resolve, reject) => {
    const majorData = getState().resume.majorData || []
    if (!isUpdate && majorData.length > 0) {
      resolve(majorData)
      return
    }

    // 优先从本地缓存获取
    const storageMajorData = wx.getStorageSync && wx.getStorageSync('majorData')
    if (storageMajorData && storageMajorData.length > 0) {
      dispatch(actions.setMajorData(storageMajorData))
      resolve(storageMajorData)
      return
    }

    wx.request({
      url: 'https://cdn6.haitou.cc/major/major.json',
      method: 'GET',
      success: (res) => {
        // 由于res.data可能为string或ArrayBuffer类型，需先判断类型
        let data: any = {}
        if (typeof res.data === 'object' && res.data !== null) {
          data = res.data
        }
        const list = Array.isArray(data.list) ? data.list : []
        const major = Array.isArray(data.major) ? data.major : []
        const majorList = list.map((item) => {
          const children = []
          major.forEach((itm) => {
            if (itm.degree == item.id) {
              children.push({
                label: itm.name,
                value: `${itm.id}-${itm.name}`,
              })
            }
          })
          return { label: item.name, value: item.id, children }
        })
        // 存入本地缓存
        wx.setStorage && wx.setStorage({
          key: 'majorData',
          data: majorList,
        })
        dispatch(actions.setMajorData(majorList))
        resolve(majorList)
      },
      fail: (err) => {
        // 失败时返回空数组
        dispatch(actions.setMajorData([]))
        resolve([])
      },
    })
  })
}

export const resumeName = name
export const resumeReducer = reducer
export const resumeActions = { ...actions, fetchGetDetailOther, fetchGlobalConfig, fetchResumeExist, getResumeConfig, fetchMajorData }
