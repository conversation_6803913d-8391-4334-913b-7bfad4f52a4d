/*
 * @Date: 2022-01-28 09:29:00
 * @Description:
 */
module.exports = {
  globals: {
    wx: true,
    Behavior: true,
    Page: true,
    App: true,
    getApp: true,
    Component: true,
    getCurrentPages: true,
    MiniConfig: true,
    YModels: true,
    IModels: true,
    // 环境变量
    ENV_IS_WEAPP: true,
    ENV_IS_SWAN: true,
    ENV_IS_TT: true,
    ENV_MODE: true,
    ENV_DEVELOPMENT: true,
    ENV_MINI_TYPE: true,
    ENV_SUB: true,
    /** 请求超时时间-默认一分钟(60000)，微信官方也是一分钟 */
    ENV_REQUEST_TIMEOUT: true
  },
  env: {
    browser: true,
    es2021: true
  },
  extends: ['eslint:recommended', 'plugin:@typescript-eslint/recommended', 'eslint-config-airbnb-base', 'plugin:sonarjs/recommended'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 12,
    sourceType: 'module'
  },
  plugins: ['@typescript-eslint', 'sonarjs'],
  rules: {
    'no-bitwise': 0,
    'no-plusplus': 0,
    'linebreak-style': 0, // LF
    'prefer-promise-reject-errors': 0, // 允许 Promise.reject('')
    'import/no-unresolved': 0,
    'import/extensions': 0,
    'import/no-extraneous-dependencies': 0,
    'no-unused-expressions': 0,
    'func-names': 0,
    'arrow-body-style': 0,
    'no-underscore-dangle': 0,
    'no-async-promise-executor': 0,
    'no-param-reassign': 0,
    '@typescript-eslint/ban-types': 0,
    'import/prefer-default-export': 0,
    'arrow-parens': 0,
    'no-use-before-define': 0,
    'no-shadow': 0, // 变量名字和外部名字重复
    'no-console': ['error', { allow: ['warn', 'error'] }], // 禁止使用console.log
    eqeqeq: 0, // === 关闭
    'object-curly-newline': ['error', { ObjectPattern: 'never' }], // 对象换行规则
    semi: ['error', 'never'], // 禁止分号
    curly: ['error', 'all'], // if 语句后面跟大括号
    'no-param-reassign': ['error', { props: true, ignorePropertyModificationsFor: ['state', 'item'] }],
    'brace-style': ['error', '1tbs', { allowSingleLine: false }], // 大括号风格
    camelcase: 'off', // 驼峰
    '@typescript-eslint/no-empty-function': 0, // 空方法
    '@typescript-eslint/explicit-module-boundary-types': 0,
    '@typescript-eslint/no-explicit-any': 0,
    'max-len': ['error', { code: 300 }],
    'no-undef': 0,
    'no-unused-vars': 0, // 未使用的变量，需配合下面的规则使用
    '@typescript-eslint/no-unused-vars': ['warn'], // 未使用的变量
    'max-classes-per-file': 0, // 一个文件可以声明多个class
    'class-methods-use-this': 0, // class 方法非要使用this
    'sonarjs/cognitive-complexity': ['error', 25],
    curly: 0,
    'default-case': 0,
    '@typescript-eslint/no-var-requires': 0,
    'global-require': 0,
    'no-param-reassign': 0,
    'no-trailing-spaces': [
      'error',
      {
        skipBlankLines: false,
        ignoreComments: true
      }
    ],
    'comma-dangle': 'off'
  }
}
