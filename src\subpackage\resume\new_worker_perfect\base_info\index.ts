/*
 * @Date: 2024-11-12 09:24:47
 * @Description: 基础信息
 */

import dayjs from '@/lib/dayjs/index'
import { RootState } from '@/store'
import { getLastNextBtnText, getResumeProcessData, saveNewWorkFillInProcessData } from '@/utils/helper/resume/utils'

Page(
  class extends wx.$.Page {
    data = {
      baseInfo: {
        name: '',
        gender: 0,
        birthDay: `${dayjs()
          .subtract(32, 'year')
          .format('YYYY')}-06`
      },
      processConfig: {},
      // 下一步流程path
      nextPath: '',
      // 上一步流程path
      prevPath: '',
      /** 跳过目的页面 */
      jumpPath: '',
      bottomHeight: 0,
      /** 下一步的文案 */
      btnText: '下一步'
    }

    useStore = (state: RootState) => {
      return {
        newWorkFillInProcessJsonData: state.resume.newWorkFillInProcessJsonData
      }
    }

    async onLoad() {
      const processInfo = await getResumeProcessData()
      const btnText = getLastNextBtnText(processInfo.processConfig.name)
      this.setData({ ...processInfo, baseInfo: processInfo.moduleValues || this.data.baseInfo, btnText })
    }

    onChange(e: any) {
      const baseInfo = wx.$.u.deepClone(this.data.baseInfo)
      const { dataset } = e.currentTarget || {}
      const { type } = dataset || {}
      let value = e.detail.value || dataset.value || ''
      if (type === 'name' && value) {
        !/^[\u4e00-\u9fa5]+$/.test(value) && (value = baseInfo.name)
        value.length > 5 && (value = value.slice(0, 5))
      }
      if (type === 'birthDay') {
        value = dayjs(value).format('YYYY-MM')
      }
      baseInfo[type] = value
      this.setData({ baseInfo })
    }

    /** 提交 */
    onSubmit(e) {
      const { fn } = e.detail || {}
      const { baseInfo, nextPath, processConfig }: any = this.data
      if (baseInfo.name === '' || !/^[\u4e00-\u9fa5]{2,5}$/.test(baseInfo.name)) {
        wx.$.msg('请填写真实的个人姓名')
        return
      }
      if (!baseInfo.gender) {
        wx.$.msg('请选择性别')
        return
      }

      // 保存数据到 newWorkFillInProcessJsonData
      saveNewWorkFillInProcessData(processConfig.type, {
        name: baseInfo.name,
        gender: baseInfo.gender,
        birthDay: baseInfo.birthDay
      })

      // 取出并打印 newWorkFillInProcessJsonData 的数据
      const { newWorkFillInProcessJsonData } = this.data as any

      console.log('newWorkFillInProcessJsonData:', newWorkFillInProcessJsonData, processConfig, nextPath)

      wx.$.javafetch['POST/resume/v3/newflow/data/save']({
        stepCode: processConfig?.stepCode || processConfig?.name,
        data: newWorkFillInProcessJsonData
      })
        .then(res => {
          console.log('723i4723444444444957932', res)

          if (res.code === 0) {
            console.log('nnnnnnnnnnnnnnnnnnn', nextPath)

            fn && fn(baseInfo)

            console.log('991', nextPath)
            wx.$.nav.replace(nextPath)
          }
        })
        .catch(() => {})

      //! !需要删除
      wx.$.javafetch['POST/resume/v3/prepub/pubUserInfo']({
        itemId: 1,
        ...baseInfo
      }).catch(() => {})
    }

    onFocus() {
      // const { height } = e.detail || {}
      // this.setData({ bottomHeight: height || 0 })
    }

    onBlur() {
      // this.setData({ bottomHeight: 0 })
    }
  }
)
